// Simple script to test your API endpoints
// Run with: node test-api.js

const baseUrl = process.env.API_URL || 'http://localhost:3000'; // Change this to your Vercel URL when deployed

async function testEndpoint(method, endpoint, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${baseUrl}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    return { status: response.status, data: result };
  } catch (error) {
    console.error(`Error testing ${method} ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log(`Testing API at: ${baseUrl}`);
  console.log('=' * 50);

  // Test root endpoint
  await testEndpoint('GET', '/');

  // Test health check
  await testEndpoint('GET', '/api/health');

  // Test a non-existent route (should return 404)
  await testEndpoint('GET', '/api/nonexistent');

  console.log('\n' + '=' * 50);
  console.log('Basic API tests completed!');
  console.log('For full testing, use a tool like Postman or create a proper test suite.');
}

// Run the tests
runTests().catch(console.error);
