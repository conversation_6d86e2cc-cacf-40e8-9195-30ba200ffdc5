# Environment Configuration for Room Expense Tracker Backend

# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/room-expense-tracker?retryWrites=true&w=majority

# JWT Secret (use a strong, random string)
JWT_SECRET=your-super-secret-jwt-key-here

# Server Configuration
PORT=5000
NODE_ENV=production

# Note: When deploying to Vercel, set these environment variables in your Vercel dashboard
# Go to: Project Settings > Environment Variables
