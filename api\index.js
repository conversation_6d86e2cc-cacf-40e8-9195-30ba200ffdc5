const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const morgan = require("morgan");
const dotenv = require("dotenv");

// Load environment variables
const path = require("path");
const result = dotenv.config({ path: path.join(__dirname, "../.env") });
if (result.error) {
  console.error("Error loading .env file:", result.error);
}

// Set environment
process.env.NODE_ENV = process.env.NODE_ENV || "production";

// Import routes
const userRoutes = require("../routes/userRoutes");
const expenseRoutes = require("../routes/expenseRoutes");
const authRoutes = require("../routes/authRoutes");
const summaryRoutes = require("../routes/summaryRoutes");

// Create Express app
const app = express();

// Middleware
app.use(
  cors({
    origin: "*", // Allow all origins
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());

// Only use morgan in development
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Routes
app.use("/api/users", userRoutes);
app.use("/api/expenses", expenseRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/summary", summaryRoutes);

// Root route
app.get("/", (req, res) => {
  res.json({
    message: "Room Expense Tracker API is running...",
    status: "success",
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});

// Health check route
app.get("/api/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  res.status(statusCode).json({
    status: "error",
    statusCode,
    message: err.message,
    stack: process.env.NODE_ENV === "production" ? null : err.stack,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: "error",
    statusCode: 404,
    message: "Route not found",
    path: req.path
  });
});

// Connect to MongoDB (for serverless, we'll connect on each request)
let isConnected = false;

const connectToDatabase = async () => {
  if (isConnected) {
    return;
  }

  try {
    if (!process.env.MONGO_URI) {
      console.warn("MONGO_URI not found in environment variables");
      return;
    }

    await mongoose.connect(process.env.MONGO_URI, {
      bufferCommands: false,
      bufferMaxEntries: 0,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    isConnected = true;
    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("MongoDB connection error:", error);
    // Don't throw error, let the app run without database
  }
};

// Middleware to ensure database connection
app.use(async (req, res, next) => {
  await connectToDatabase();
  next();
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("UNHANDLED REJECTION:", err.name, err.message);
});

// Export the Express app for Vercel
module.exports = app;
